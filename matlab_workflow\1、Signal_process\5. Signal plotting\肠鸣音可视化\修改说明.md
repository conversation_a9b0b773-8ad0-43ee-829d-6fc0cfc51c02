# photograph.m 交互式选择功能修复说明

## 问题描述

原始代码在第94行出现错误："在数据文件中找不到tt1"。这是因为：

1. **硬编码变量名问题**：原代码硬编码检查名为 `tt1` 的变量
2. **实际数据结构**：数据文件 `data4_5min_seg005_tt_yes_15.mat` 包含的变量名为：
   - `data4_5min_seg005_tt1`
   - `data4_5min_seg005_tt2`
3. **变量名不匹配**：代码期望的 `tt1` 与实际的 `data4_5min_seg005_tt1` 不匹配
4. **用户需求**：用户希望能够在命令行中选择处理tt1还是tt2数据

## 修复方案

### 1. 动态变量识别与交互式选择

**原代码（第92-104行）：**
```matlab
% 检查加载的数据中是否存在tt1
if ~exist('tt1', 'var')
    error('在数据文件中找不到tt1');
end

% 确保tt1是timetable格式
if ~istimetable(tt1)
    error('tt1不是timetable格式');
end

% 从timetable中提取时间和信号数据
time = tt1.Time - tt1.Time(1); % 将时间归零
signal = tt1.Variables; % 假设timetable只有一列数据
```

**修复后代码（交互式选择版本）：**
```matlab
% 动态检查加载的数据中的timetable变量
loaded_vars = who; % 获取所有加载的变量名
timetable_vars = {};
timetable_var_name = '';

% 查找所有timetable类型的变量
for i = 1:length(loaded_vars)
    var_name = loaded_vars{i};
    if exist(var_name, 'var') && istimetable(eval(var_name))
        timetable_vars{end+1} = var_name;
    end
end

% 检查是否找到timetable变量
if isempty(timetable_vars)
    error('在数据文件中找不到任何timetable格式的变量');
elseif length(timetable_vars) == 1
    % 只有一个timetable变量，直接使用
    timetable_var_name = timetable_vars{1};
    fprintf('找到timetable变量: %s\n', timetable_var_name);
else
    % 多个timetable变量，让用户选择
    fprintf('\n发现多个timetable变量:\n');
    for i = 1:length(timetable_vars)
        var_name = timetable_vars{i};
        var_data = eval(var_name);
        time_duration = seconds(var_data.Time(end) - var_data.Time(1));
        fprintf('  [%d] %s (数据点: %d, 时长: %.1f秒)\n', ...
            i, var_name, height(var_data), time_duration);
    end

    % 用户选择
    while true
        choice = input(sprintf('\n请选择要处理的变量 (1-%d): ', length(timetable_vars)));

        % 检查输入是否有效
        if isnumeric(choice) && isscalar(choice) && ...
           choice >= 1 && choice <= length(timetable_vars) && ...
           choice == round(choice)
            timetable_var_name = timetable_vars{choice};
            fprintf('您选择了: %s\n', timetable_var_name);
            break;
        else
            fprintf('无效选择，请输入1到%d之间的整数。\n', length(timetable_vars));
        end
    end
end

% 获取选定的timetable变量
selected_tt = eval(timetable_var_name);

% 从timetable中提取时间和信号数据
time = selected_tt.Time - selected_tt.Time(1); % 将时间归零
signal = selected_tt.Variables; % 假设timetable只有一列数据
```

### 2. 交互式变量选择逻辑

修复后的代码实现了以下交互式选择逻辑：

1. **扫描所有变量**：使用 `who` 函数获取所有加载的变量名
2. **识别timetable变量**：检查每个变量是否为timetable类型
3. **智能选择策略**：
   - 如果只有一个timetable变量，直接使用
   - 如果有多个timetable变量，显示选择菜单让用户选择
4. **交互式界面**：
   - 显示所有可用的timetable变量及其基本信息（数据点数、时长）
   - 提供编号选择界面
   - 输入验证和错误处理
   - 重新提示直到用户输入有效选择
5. **用户反馈**：通过 `fprintf` 告知用户选择了哪个变量

### 3. 文档更新

同时更新了代码注释，反映新的功能：

- **数据要求**：从"数据变量: tt1 (timetable格式)"改为"数据变量: 任何timetable格式的变量（优先选择包含'tt1'的变量）"
- **使用示例**：增加了"程序自动识别并选择合适的timetable变量"的说明
- **注意事项**：更新为更灵活的变量要求说明
- **错误处理**：增加了"多变量情况下的智能选择逻辑"

## 修复效果

### 处理能力提升

1. **兼容性增强**：能够处理各种命名格式的timetable变量
2. **自动识别**：无需手动修改变量名，程序自动识别合适的变量
3. **多变量支持**：能够处理包含多个timetable变量的数据文件
4. **用户友好**：提供清晰的变量选择反馈信息

### 具体应用场景

对于数据文件 `data4_5min_seg005_tt_yes_15.mat`：
- 包含变量：`data4_5min_seg005_tt1` 和 `data4_5min_seg005_tt2`
- 程序会显示交互式选择界面：
  ```
  发现多个timetable变量:
    [1] data4_5min_seg005_tt1 (数据点: 771000, 时长: 300.0秒)
    [2] data4_5min_seg005_tt2 (数据点: 771000, 时长: 300.0秒)

  请选择要处理的变量 (1-2):
  ```
- 用户可以输入 `1` 选择tt1数据，或输入 `2` 选择tt2数据
- 程序确认选择："您选择了: data4_5min_seg005_tt1"

## 测试验证

创建了测试脚本 `test_photograph_fix.m` 用于验证修复效果：
- 加载测试数据文件
- 显示所有变量信息
- 验证变量选择逻辑
- 检查数据质量

## 总结

此次修复解决了原代码的硬编码变量名问题，并增加了交互式选择功能，使程序具备了更强的适应性和用户友好性。修复后的代码能够：

1. **自动适应**不同的变量命名格式
2. **交互式选择**让用户自主选择要处理的timetable变量
3. **输入验证**确保用户输入的有效性，提供错误处理和重新提示
4. **信息展示**显示每个变量的详细信息（数据点数、时长）帮助用户做出选择
5. **用户反馈**提供清晰的选择确认信息
6. **保持兼容**原有的可视化功能完整性

### 新增功能特点

- **用户自主权**：用户可以根据需要选择处理tt1还是tt2数据
- **信息透明**：显示每个变量的基本统计信息
- **错误容错**：输入验证和重新提示机制
- **操作简便**：简单的数字选择界面

这样的修复不仅解决了技术问题，还大大提升了用户体验，让用户能够灵活地选择要分析的数据，满足了用户的具体需求。
