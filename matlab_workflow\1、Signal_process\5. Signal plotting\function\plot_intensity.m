function plot_intensity(t, signal, fs, position, rows, cols, index, title_text)
%PLOT_INTENSITY 肠鸣音信号强度分析和绘制函数
%   计算并绘制肠鸣音信号在特定频段(100-1000Hz)的强度变化图。
%   该函数专门用于医学研究中肠鸣音活动强度的量化分析和可视化。
%
%   语法:
%   plot_intensity(t, signal, fs, position, rows, cols, index, title_text)
%
%   输入参数:
%   t          - 时间向量 (duration数组，MATLAB时间格式)
%   signal     - 输入信号数据 (数值向量)
%   fs         - 采样频率 (标量，单位：Hz，通常为2570Hz)
%   position   - 图形窗口位置 ([left, bottom, width, height])
%   rows       - 子图行数 (正整数)
%   cols       - 子图列数 (正整数)
%   index      - 当前子图索引 (正整数)
%   title_text - 图形标题 (字符串)
%
%   输出参数:
%   无 - 直接生成强度变化图显示
%
%   算法原理:
%   1. 使用短时傅里叶变换计算信号频谱图
%   2. 提取100-1000Hz频段的功率谱密度
%   3. 对该频段进行功率积分得到瞬时强度
%   4. 插值到原始时间网格进行可视化
%
%   技术参数:
%   - 窗口长度: 0.05秒 (50ms)
%   - 窗口重叠: 90% (45ms重叠)
%   - FFT点数: 采样率的一半 (fs/2)
%   - 分析频段: 100-1000Hz (肠鸣音主要频段)
%   - 频率分辨率: fs/nfft ≈ 2.57Hz (当fs=2570Hz时)
%
%   频段选择依据:
%   - 100Hz: 肠鸣音下限频率
%   - 1000Hz: 肠鸣音上限频率
%   - 频率索引: 39-389 (对应100-1000Hz)
%   - 医学意义: 该频段包含大部分肠鸣音能量
%
%   图形特征:
%   - 信号颜色: 深蓝色 RGB[56,82,151]
%   - Y轴范围: [0, 0.025] (强度单位)
%   - X轴范围: [0, 300] 秒 (5分钟)
%   - X轴刻度: [0, 50, 100, 150, 200, 250, 300]秒
%   - 字体设置: Times New Roman, 加粗, 16pt刻度, 18pt标签
%
%   强度计算方法:
%   1. 频谱图计算: spectrogram(signal, win, ov, nfft, fs)
%   2. 频段提取: P_C(39:389, :) 对应100-1000Hz
%   3. 强度积分: sum(P_C(freq_range, :)) 沿频率轴求和
%   4. 时间插值: interp1() 插值到原始时间网格
%
%   应用场景:
%   - 肠鸣音活动强度量化分析
%   - 消化系统功能评估
%   - 病理状态监测和诊断
%   - 药物效果评价
%   - 科研数据统计分析
%
%   示例:
%   % 基本用法
%   fs = 2570; % 采样率
%   t = seconds(0:1/fs:299); % 5分钟时间向量
%   signal = randn(length(t),1); % 随机信号
%   position = [200, 200, 1500, 900];
%   plot_intensity(t, signal, fs, position, 1, 1, 1, '肠鸣音强度');
%
%   % 多通道对比
%   figure('Position', [100, 100, 1200, 800]);
%   plot_intensity(t, signal1, fs, position, 2, 1, 1, '通道1强度');
%   plot_intensity(t, signal2, fs, position, 2, 1, 2, '通道2强度');
%
%   注意事项:
%   - 时间向量t必须为duration格式
%   - 强度值范围[0,0.025]适用于归一化信号
%   - 插值过程可能引入轻微的时间延迟
%   - 频段选择基于肠鸣音的生理特征
%   - 需要足够长的信号以获得稳定的强度估计
%
%   医学意义:
%   - 强度峰值: 对应肠鸣音活跃期
%   - 强度低谷: 对应肠道相对静止期
%   - 强度变化: 反映肠道蠕动节律
%   - 异常模式: 可能指示消化系统疾病
%
%   参见: PLOT_WAVEFORM, PLOT_SPECTROGRAM, SPECTROGRAM, INTERP1
%
%   作者: [医学信号处理团队]
%   创建日期: [创建日期]
%   版本: 1.0

    % 计算声音强度 - 使用短时傅里叶变换分析
    w = 0.05; % 设置窗口宽度
    win = round(fs*w); % 计算窗口大小
    ov = round(fs*w*0.9); % 计算重叠部分
    nfft = round(fs*0.5); % 设置FFT点数
    [~,~,T_C,P_C] = spectrogram(signal,win,ov,nfft,fs); % 计算频谱图

    % 计算声音强度
    % 频率分辨率 = fs/nfft ≈ 2.57Hz
    % 100Hz对应索引 ≈ 39
    % 1000Hz对应索引 ≈ 389
    freq_range = 39:389;  % 对应约100-1000Hz
    I_BowelSound = sum(P_C(freq_range,:)); % 计算肠鸣音强度

    % 将T_C转换为与time相同的时间范围
    T_C_adjusted = T_C * (seconds(t(end)) - seconds(t(1))) / T_C(end);

    % 使用插值将强度值调整到与time相同的时间点
    I_BowelSound_interpolated = interp1(T_C_adjusted, I_BowelSound, seconds(t));

    % 显示强度图
    figure('Position', position);
    subplot(rows, cols, index);
    color1 = [56/255, 82/255, 151/255];
    plot(seconds(t), I_BowelSound_interpolated, 'Color', color1) % 绘制强度
    ax = gca;
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.YLim = [0 0.025];
    ax.XLim = [0 300];
    xticks([0 50 100 150 200 250 300]); % 自定义纵坐标刻度
    xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Intensity', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    
end 