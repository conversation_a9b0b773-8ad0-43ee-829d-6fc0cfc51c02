function plot_spectrogram(data, fs, position, rows, cols, index, title_text)
%PLOT_SPECTROGRAM 肠鸣音信号频谱图绘制函数
%   计算并绘制信号的时频谱图，使用短时傅里叶变换(STFT)分析肠鸣音信号
%   的频域特征。该函数提供专业的科研级别频谱图可视化，适用于医学信号
%   处理和肠鸣音特征分析研究。
%
%   语法:
%   plot_spectrogram(data, fs, position, rows, cols, index, title_text)
%
%   输入参数:
%   data       - 输入信号数据 (数值向量)
%   fs         - 采样频率 (标量，单位：Hz，通常为2570Hz)
%   position   - 图形窗口位置 ([left, bottom, width, height])
%   rows       - 子图行数 (正整数)
%   cols       - 子图列数 (正整数)
%   index      - 当前子图索引 (正整数)
%   title_text - 图形标题 (字符串)
%
%   输出参数:
%   无 - 直接生成频谱图显示
%
%   技术参数:
%   - 窗口长度: 0.05秒 (50ms)
%   - 窗口重叠: 90% (45ms重叠)
%   - FFT点数: 采样率的一半 (fs/2)
%   - 窗口类型: 汉明窗 (MATLAB默认)
%   - 颜色范围: [-90, -50] dB
%   - 颜色映射: Inferno (感知均匀)
%
%   图形特征:
%   - X轴: 时间 (秒，范围0-300秒)
%   - Y轴: 频率 (Hz)
%   - 颜色: 功率谱密度 (dB)
%   - 视角: 俯视图 (0°, 90°)
%   - 字体: Times New Roman, 加粗, 16pt刻度, 18pt标签
%   - X轴刻度: [0, 50, 100, 150, 200, 250, 300]秒
%
%   频率分辨率:
%   - 理论分辨率: fs/nfft ≈ 2.57Hz (当fs=2570Hz时)
%   - 时间分辨率: 窗口长度50ms，重叠90%，实际步进5ms
%   - 适用频段: 0Hz至奈奎斯特频率(fs/2)
%
%   肠鸣音分析特性:
%   - 低频段(20-100Hz): 正常肠蠕动
%   - 中频段(100-400Hz): 典型肠鸣音频段
%   - 高频段(400-800Hz): 异常肠鸣音或噪声
%
%   应用场景:
%   - 肠鸣音信号频域特征分析
%   - 信号降噪算法效果评估
%   - 频谱污染和异常检测
%   - 科研论文图形制作
%   - 多通道信号频域对比
%
%   示例:
%   % 基本用法
%   fs = 2570; % 采样率
%   t = (0:fs*5-1)/fs; % 5秒信号
%   signal = sin(2*pi*100*t) + 0.5*sin(2*pi*300*t); % 双频信号
%   position = [200, 200, 1500, 900];
%   plot_spectrogram(signal, fs, position, 1, 1, 1, '双频测试信号');
%
%   % 降噪前后对比
%   figure('Position', [100, 100, 1200, 400]);
%   plot_spectrogram(noisy_signal, fs, position, 1, 2, 1, '降噪前');
%   plot_spectrogram(clean_signal, fs, position, 1, 2, 2, '降噪后');
%
%   注意事项:
%   - 需要inferno颜色映射函数支持
%   - 信号长度应足够长以获得良好的频率分辨率
%   - 采样率应满足奈奎斯特定理
%   - 颜色范围[-90,-50]dB适用于归一化信号
%   - 固定显示300秒时间窗口
%
%   依赖函数:
%   - spectrogram: MATLAB信号处理工具箱函数
%   - inferno: 感知均匀颜色映射函数
%
%   参见: SPECTROGRAM, PLOT_WAVEFORM, PLOT_INTENSITY, INFERNO
%
%   作者: [医学信号处理团队]
%   创建日期: [创建日期]
%   版本: 1.0
    w = 0.05;
    win = round(fs * w);
    ov = round(fs * w * 0.9);
    nfft = round(fs * 0.5);
    [S, F, T, P] = spectrogram(data, win, ov, nfft, fs);
    
    figure('Position', position); % 设置图框位置和大小，[left, bottom, width, height]
    subplot(rows, cols, index);
    surf(T, F, 10 * log10(P), 'edgecolor', 'none');
    axis tight;
    view(0, 90);
    caxis([-90 -50]);
    colorbar;
    colormap(inferno);
    ax = gca;
    ax.FontSize = 16; % 设置刻度尺文字大小
    ax.FontName = 'Times New Roman'; % 设置刻度尺字体
    ax.FontWeight = "bold";
    ax.XLim = [0 300];
    xticks([0 50 100 150 200 250 300]); % 自定义纵坐标刻度
    xlabel('Time(s)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    ylabel('Freq.(Hz)', 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
    title(title_text, 'FontSize', 18, 'FontName', 'Times New Roman', 'FontWeight', 'bold');
end